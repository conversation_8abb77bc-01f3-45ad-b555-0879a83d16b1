# 🌟 LED Predict MQTT IoT System

## 📋 Overview

LED_Predict adalah sistem IoT berbasis ESP32 yang mengintegrasikan 5 sensor canggih untuk monitoring lingkungan secara real-time. Sistem ini menggunakan protokol MQTT untuk transfer data dan dilengkapi dengan NTP time synchronization untuk timestamp yang akurat.

## 🔧 Hardware Components

### 📱 Microcontroller
- **ESP32 Dev Module** - Main controller dengan WiFi built-in

### 🌡️ Sensors
1. **DHT22** - Temperature & Humidity Sensor
   - Range: -40°C to 125°C, 0-100% RH
   - Accuracy: ±0.5°C, ±2-5% RH
   - Pin: GPIO5

2. **BH1750** - Digital Light Sensor
   - Range: 1-65535 lux
   - Resolution: 1 lux
   - Interface: I2C (0x23)

3. **INA219** - Current & Voltage Sensor
   - Voltage: 0-26V
   - Current: ±3.2A
   - Interface: I2C (0x40)

4. **AMG8833** - 8x8 Thermal Imaging Sensor
   - Resolution: 8x8 = 64 thermal pixels
   - Range: 0°C to 80°C
   - Interface: I2C
   - Use: Presence detection, thermal mapping

5. **AS7341** - 11-Channel Spectral Color Sensor
   - Channels: 8 spectral + Clear + NIR + Flicker
   - Wavelengths: 415nm, 445nm, 480nm, 515nm, 555nm, 590nm, 630nm, 680nm
   - Interface: I2C
   - Built-in LED: 0-150mA controllable

## 🔌 Wiring Diagram

```
ESP32 Pin    | Component        | Function
-------------|------------------|------------------
3.3V         | All Sensors      | Power Supply
GND          | All Sensors      | Ground
GPIO5        | DHT22            | Data Pin
GPIO21       | I2C Sensors      | SDA (Data)
GPIO22       | I2C Sensors      | SCL (Clock)
```

### I2C Devices:
- **BH1750**: Address 0x23
- **INA219**: Address 0x40
- **AMG8833**: Default I2C address
- **AS7341**: Default I2C address

## 📡 MQTT Configuration

### 🌐 Broker Settings
```cpp
// Public Broker (Default)
const char* mqtt_server_public = "broker.hivemq.com";
const int mqtt_port_public = 1883;

// Secure Broker (Optional)
const char* mqtt_server_secure = "your-secure-broker.com";
const int mqtt_port_secure = 8883;
```

### 📊 MQTT Topics

#### 1. Main Sensor Data
**Topic**: `smartlamp/data`
```json
{
  "ts": 346923,
  "dt": "2024-01-15 14:30:25",
  "cnt": 11,
  "temp": 25.3,
  "hum": 60.2,
  "lux": 150.0,
  "volt": 3.30,
  "curr": 120.0,
  "dev": "ESP32_SmartLamp_A93404",
  "int": 5000,
  "dht22": true,
  "bh1750": true,
  "ina219": true,
  "amg8833": true,
  "as7341": true
}
```

#### 2. Thermal Imaging Data
**Topic**: `smartlamp/thermal`
```json
{
  "timestamp": 346923,
  "datetime": "2024-01-15 14:30:25",
  "device_id": "ESP32_SmartLamp_A93404",
  "avg_temp": 25.5,
  "max_temp": 27.2,
  "min_temp": 23.8,
  "presence": true,
  "thermal_data": [
    [25.1, 25.3, 25.5, 25.7, 25.9, 26.1, 26.3, 26.5],
    [25.0, 25.2, 25.4, 25.6, 25.8, 26.0, 26.2, 26.4],
    [24.9, 25.1, 25.3, 25.5, 25.7, 25.9, 26.1, 26.3],
    [24.8, 25.0, 25.2, 25.4, 25.6, 25.8, 26.0, 26.2],
    [24.7, 24.9, 25.1, 25.3, 25.5, 25.7, 25.9, 26.1],
    [24.6, 24.8, 25.0, 25.2, 25.4, 25.6, 25.8, 26.0],
    [24.5, 24.7, 24.9, 25.1, 25.3, 25.5, 25.7, 25.9],
    [24.4, 24.6, 24.8, 25.0, 25.2, 25.4, 25.6, 25.8]
  ]
}
```

#### 3. Spectral Color Data
**Topic**: `smartlamp/spectral`
```json
{
  "timestamp": 346923,
  "datetime": "2024-01-15 14:30:25",
  "device_id": "ESP32_SmartLamp_A93404",
  "415nm": 1234,
  "445nm": 2345,
  "480nm": 3456,
  "515nm": 4567,
  "555nm": 5678,
  "590nm": 6789,
  "630nm": 7890,
  "680nm": 8901,
  "clear": 9012,
  "nir": 1023,
  "cct": 4000,
  "settings": {
    "atime": 100,
    "astep": 999,
    "gain": 4
  }
}
```

#### 4. Command Topic
**Topic**: `smartlamp/cmd`

#### 5. Status Topic
**Topic**: `smartlamp/status`

## 🎮 MQTT Commands

### 📊 System Commands
```bash
# Get system status
mosquitto_pub -h broker.hivemq.com -t "smartlamp/cmd" -m '{"action":"get_status"}'

# Force immediate reading
mosquitto_pub -h broker.hivemq.com -t "smartlamp/cmd" -m '{"action":"force_reading"}'

# Set reading interval (5-300 seconds)
mosquitto_pub -h broker.hivemq.com -t "smartlamp/cmd" -m '{"action":"set_interval","interval":10}'
```

### 🧪 Sensor Testing Commands
```bash
# Test DHT22 sensor
mosquitto_pub -h broker.hivemq.com -t "smartlamp/cmd" -m '{"action":"test_dht22"}'

# Reset DHT22 sensor
mosquitto_pub -h broker.hivemq.com -t "smartlamp/cmd" -m '{"action":"reset_dht22"}'

# Test thermal sensor
mosquitto_pub -h broker.hivemq.com -t "smartlamp/cmd" -m '{"action":"test_thermal"}'

# Test spectral sensor
mosquitto_pub -h broker.hivemq.com -t "smartlamp/cmd" -m '{"action":"test_spectral"}'

# Test MQTT connection
mosquitto_pub -h broker.hivemq.com -t "smartlamp/cmd" -m '{"action":"test_mqtt"}'
```

### 💡 AS7341 LED Control
```bash
# Turn on LED with 25mA current
mosquitto_pub -h broker.hivemq.com -t "smartlamp/cmd" -m '{"action":"set_led","current":25}'

# Turn off LED
mosquitto_pub -h broker.hivemq.com -t "smartlamp/cmd" -m '{"action":"set_led","current":0}'

# Maximum LED current (150mA)
mosquitto_pub -h broker.hivemq.com -t "smartlamp/cmd" -m '{"action":"set_led","current":150}'
```

## 🖥️ Serial Monitor Output

### 🚀 Startup Sequence
```
🚀 Smart Lamp Starting...
📡 MQTT: broker.hivemq.com:1883 (Public)
🔧 Sensor verification: Will check sensors for first 5 readings
🕐 NTP: Synchronized ✓
Current time: 2024-01-15 14:30:25 WIB
Sensors will be verified during first readings...
✅ Ready!
```

### 🔍 Sensor Verification
```
🔍 Verifying sensors...
🔍 Testing DHT22 with internal pull-up...
Attempt 1: T=32.8°C H=52.5% ✅ SUCCESS
✅ DHT22 verified

Verifying BH1750 light sensor...
BH1750 found at address 0x23
BH1750 verified - Light Level: 29.2 lux
✅ BH1750 verified

Verifying INA219 current sensor...
INA219 found at address 0x40
INA219 verified - Bus Voltage: 0.884V, Current: -0.5mA
✅ INA219 verified

🔍 Testing AMG8833 thermal sensor...
✅ AMG8833 verified - Avg temp: 31.0°C
✅ AMG8833 verified

🔍 Testing AS7341 spectral sensor...
✅ AS7341 verified - Clear: 14
✅ AS7341 verified
```

### 📊 Detailed Mode (First 5 Readings)
```
🔍 Sensor Check #2/5: DHT22(32.8°C,52.5%) BH1750(29lux) INA219(0.89V,-0mA) AMG8833(31.0°C) AS7341(2700K)
📊 Sensors: 5/5 working ✅

┌─────────────────────────────────────────┐
│ 🌡️  Temperature:     32.8°C          │
│ 💧 Humidity:        52.5%           │
│ 💡 Light Level:     29.2 lux        │
│ ⚡ Voltage:        0.888V           │
│ 🔌 Current:         -0.3mA          │
│ 🔋 Power:          -0.27mW          │
│ 🌡️  Thermal Avg:     31.0°C          │
│ 🔥 Thermal Max:     33.0°C          │
│ 👤 Presence:      DETECTED          │
│ 🌈 Color Temp:      2700K          │
│ ☀️  Clear:             14            │
│ 🔴 NIR:                9            │
└─────────────────────────────────────────┘

📤 Sending... ✅
Thermal data sent: ✅
Spectral data sent: ✅
```

### 📱 Simple Mode (Normal Operation)
```
📊 Reading #6: DHT22(32.8°C,52.5%) BH1750(29lux) INA219(0.89V,-0mA) AMG8833(31.0°C) AS7341(2700K)
📊 Sensors: 5/5 working ✅
📤 Sending... ✅
Thermal data sent: ✅
Spectral data sent: ✅
📶 WiFi:✓ 📡 MQTT:✓ 🕐 2024-01-15 14:30:25 WIB ⏰ Next in 5s
```

## ⚙️ Configuration

### 📶 WiFi Settings
```cpp
const char *ssid = "Your_WiFi_SSID";
const char *password = "Your_WiFi_Password";
```

### ⏰ NTP Time Synchronization
```cpp
const char* ntpServer = "pool.ntp.org";
const long gmtOffset_sec = 7 * 3600; // WIB = UTC+7
const int daylightOffset_sec = 0; // No daylight saving in Indonesia
```

### 📊 Reading Interval
```cpp
unsigned long interval = 5000; // 5 seconds (adjustable 5-300s)
```

### 🔧 Sensor Check Configuration
```cpp
const int MAX_SENSOR_CHECKS = 5; // Verify sensors for first 5 readings
```

## 🛠️ Installation & Setup

### 📋 Prerequisites
1. **PlatformIO IDE** or **Arduino IDE**
2. **ESP32 Board Package**
3. **Required Libraries** (auto-installed via platformio.ini)

### 📚 Required Libraries
```ini
lib_deps =
    adafruit/DHT sensor library@^1.4.4
    claws/BH1750@^1.3.0
    adafruit/Adafruit INA219@^1.2.1
    sparkfun/SparkFun GridEYE AMG88 Library@^1.0.2
    adafruit/Adafruit AS7341@^1.3.4
    adafruit/Adafruit BusIO@^1.14.1
    knolleary/PubSubClient@^2.8
    bblanchon/ArduinoJson@^6.21.3
```

### 🔧 Setup Steps
1. **Clone Repository**
   ```bash
   git clone <repository-url>
   cd Smart-Lamp-MQTT2
   ```

2. **Configure WiFi**
   - Edit `src/main.cpp`
   - Update WiFi credentials:
     ```cpp
     const char *ssid = "Your_WiFi_SSID";
     const char *password = "Your_WiFi_Password";
     ```

3. **Wire Hardware**
   - Connect sensors according to wiring diagram
   - Ensure proper power supply (3.3V for all sensors)

4. **Upload Firmware**
   ```bash
   pio run --target upload
   ```

5. **Monitor Serial Output**
   ```bash
   pio device monitor
   ```

## 🎯 Use Cases & Applications

### 🏠 Smart Home Automation
- **Environmental Monitoring**: Temperature, humidity, light levels
- **Presence Detection**: AMG8833 thermal imaging for occupancy
- **Energy Monitoring**: Power consumption tracking via INA219
- **Light Quality Control**: Spectral analysis for optimal lighting

### 🌱 Agriculture & Greenhouse
- **Climate Control**: Automated HVAC based on environmental data
- **Plant Health**: Spectral analysis for growth optimization
- **Irrigation Control**: Humidity-based watering systems

### 🏢 Office & Commercial
- **Energy Management**: Smart lighting and HVAC control
- **Occupancy Analytics**: Heat map generation for space utilization
- **Air Quality**: Environmental monitoring for comfort

### 🔬 Research & Development
- **Data Logging**: High-precision sensor data collection
- **Spectral Analysis**: Color science and lighting research
- **Thermal Imaging**: Heat distribution studies

## 📊 Dashboard Integration

### 🌐 MQTT Client Examples

#### Node.js Example
```javascript
const mqtt = require('mqtt');
const client = mqtt.connect('mqtt://broker.hivemq.com');

client.on('connect', () => {
  client.subscribe('smartlamp/+');
});

client.on('message', (topic, message) => {
  const data = JSON.parse(message.toString());

  switch(topic) {
    case 'smartlamp/data':
      console.log(`Temp: ${data.temp}°C, Humidity: ${data.hum}%`);
      break;
    case 'smartlamp/thermal':
      console.log(`Presence: ${data.presence}, Avg Temp: ${data.avg_temp}°C`);
      break;
    case 'smartlamp/spectral':
      console.log(`CCT: ${data.cct}K, Clear: ${data.clear}`);
      break;
  }
});
```

#### Python Example
```python
import paho.mqtt.client as mqtt
import json

def on_connect(client, userdata, flags, rc):
    client.subscribe("smartlamp/+")

def on_message(client, userdata, msg):
    topic = msg.topic
    data = json.loads(msg.payload.decode())

    if topic == "smartlamp/data":
        print(f"Temp: {data['temp']}°C, Humidity: {data['hum']}%")
    elif topic == "smartlamp/thermal":
        print(f"Presence: {data['presence']}, Avg Temp: {data['avg_temp']}°C")
    elif topic == "smartlamp/spectral":
        print(f"CCT: {data['cct']}K, Clear: {data['clear']}")

client = mqtt.Client()
client.on_connect = on_connect
client.on_message = on_message
client.connect("broker.hivemq.com", 1883, 60)
client.loop_forever()
```

### 📈 Visualization Examples

#### Real-time Heatmap (JavaScript)
```javascript
// 8x8 thermal heatmap visualization
function renderThermalHeatmap(thermalData) {
  const canvas = document.getElementById('heatmap');
  const ctx = canvas.getContext('2d');

  for(let row = 0; row < 8; row++) {
    for(let col = 0; col < 8; col++) {
      const temp = thermalData[row][col];
      const color = temperatureToColor(temp);

      ctx.fillStyle = color;
      ctx.fillRect(col * 40, row * 40, 40, 40);
    }
  }
}

function temperatureToColor(temp) {
  // Convert temperature to color (blue=cold, red=hot)
  const normalized = (temp - 20) / 15; // 20-35°C range
  const hue = (1 - normalized) * 240; // Blue to Red
  return `hsl(${hue}, 100%, 50%)`;
}
```

#### Spectral Graph (Chart.js)
```javascript
// Spectral analysis chart
const spectralChart = new Chart(ctx, {
  type: 'line',
  data: {
    labels: ['415nm', '445nm', '480nm', '515nm', '555nm', '590nm', '630nm', '680nm'],
    datasets: [{
      label: 'Spectral Intensity',
      data: [data['415nm'], data['445nm'], data['480nm'], data['515nm'],
             data['555nm'], data['590nm'], data['630nm'], data['680nm']],
      borderColor: 'rgb(75, 192, 192)',
      tension: 0.1
    }]
  }
});
```

## 🔧 Troubleshooting

### ❌ Common Issues

#### WiFi Connection Problems
```
Symptoms: "WiFi connection failed" messages
Solutions:
- Check WiFi credentials in code
- Ensure 2.4GHz network (ESP32 doesn't support 5GHz)
- Check signal strength
- Restart router if needed
```

#### MQTT Connection Issues
```
Symptoms: "MQTT connection failed" messages
Solutions:
- Check broker address and port
- Verify internet connection
- Try different MQTT broker
- Check firewall settings
```

#### Sensor Not Detected
```
DHT22 Issues:
- Check GPIO5 connection
- Verify 3.3V power supply
- Try external 4.7kΩ pull-up resistor

I2C Sensor Issues:
- Check SDA (GPIO21) and SCL (GPIO22) connections
- Verify I2C addresses using scanner
- Ensure proper power supply (3.3V)
- Check for I2C conflicts
```

#### Memory Issues
```
Symptoms: Frequent reboots, unstable operation
Solutions:
- Monitor free heap in serial output
- Reduce JSON payload size if needed
- Increase delay between readings
- Check for memory leaks
```

### 🔍 Diagnostic Commands

#### I2C Scanner
```cpp
// Add to setup() for I2C device detection
Wire.begin();
for(byte address = 1; address < 127; address++) {
  Wire.beginTransmission(address);
  if(Wire.endTransmission() == 0) {
    Serial.printf("I2C device found at address 0x%02X\n", address);
  }
}
```

#### Memory Monitor
```cpp
// Add to loop() for memory monitoring
Serial.printf("Free heap: %d bytes\n", ESP.getFreeHeap());
```

## 📝 Development Notes

### 🔄 System Architecture
```
ESP32 Controller
├── WiFi Connection
├── NTP Time Sync (WIB/UTC+7)
├── MQTT Client
├── Sensor Management
│   ├── DHT22 (GPIO5)
│   ├── BH1750 (I2C 0x23)
│   ├── INA219 (I2C 0x40)
│   ├── AMG8833 (I2C)
│   └── AS7341 (I2C)
└── Data Publishing
    ├── smartlamp/data
    ├── smartlamp/thermal
    └── smartlamp/spectral
```

### 💾 Memory Usage
```
RAM:   [=         ]  14.7% (used 48028 bytes from 327680 bytes)
Flash: [=======   ]  75.0% (used 982597 bytes from 1310720 bytes)
```

### ⚡ Performance Metrics
- **Sensor Reading**: ~2-3 seconds per cycle
- **MQTT Publish**: ~100-200ms per message
- **WiFi Reconnect**: ~5-10 seconds
- **NTP Sync**: ~2-5 seconds

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🤝 Contributing

1. Fork the repository
2. Create feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit changes (`git commit -m 'Add AmazingFeature'`)
4. Push to branch (`git push origin feature/AmazingFeature`)
5. Open Pull Request

## 📞 Support

For support and questions:
- Create an issue in the repository
- Check troubleshooting section
- Review serial monitor output for diagnostic information

## 🔮 Future Enhancements

### Planned Features
- [ ] Web dashboard interface
- [ ] Data logging to SD card
- [ ] OTA (Over-The-Air) firmware updates
- [ ] Additional sensor support
- [ ] Machine learning integration for pattern recognition
- [ ] Mobile app for remote control

### Sensor Expansion Ideas
- [ ] CO2 sensor (MH-Z19)
- [ ] PM2.5 air quality sensor
- [ ] Sound level meter
- [ ] UV index sensor
- [ ] Soil moisture sensors

---

**Smart Lamp MQTT IoT System** - Advanced environmental monitoring with thermal imaging and spectral analysis capabilities. 🌟