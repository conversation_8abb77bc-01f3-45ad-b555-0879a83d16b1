#include <Arduino.h>
#include <WiFi.h>
#include <WiFiClientSecure.h>
#include <PubSubClient.h>
#include <ArduinoJson.h>
#include <DHT.h>
#include <Wire.h>
#include <BH1750.h>
#include <Adafruit_INA219.h> // Add INA219 library
#include <SparkFun_GridEYE_Arduino_Library.h> // Add AMG8833 thermal sensor library
#include <Adafruit_AS7341.h> // Add AS7341 spectral color sensor library
#include <time.h> // For NTP time
#include "certificates.h" // Include certificates

// Sensor Configuration
#define DHTPIN 5 // GPIO4 for DHT22
#define DHTTYPE DHT22
#define SDA_PIN 21 // GPIO21 for I2C SDA
#define SCL_PIN 22 // GPIO22 for I2C SCL

DHT dht(DHTPIN, DHTTYPE);
BH1750 lightMeter;
Adafruit_INA219 ina219;
GridEYE amg;
Adafruit_AS7341 as7341; // INA219 object

// MQTT Clients
WiFiClient espClientPlain;
WiFiClientSecure espClientSecure;
PubSubClient mqttClient(espClientPlain); // Will be reconfigured in setup()

// WiFi Configuration
const char *ssid = "Bismillah";
const char *password = "besoknaikhaji";

// MQTT Configuration
// ==================
// Simple choice: Secure (HiveMQ Cloud) or Non-Secure (HiveMQ Public)
// true  = HiveMQ Cloud with TLS/SSL (secure, requires authentication)
// false = HiveMQ Public without TLS (non-secure, no authentication)

const bool use_secure_mqtt = true; // Change this: true = secure, false = non-secure

// HiveMQ Cloud Configuration (Secure)
const char *mqtt_server_secure = "79102f6522304514a10e2b4a7caa2bb2.s1.eu.hivemq.cloud";
const int mqtt_port_secure = 8883;
const char *mqtt_user_secure = "ledpredict";
const char *mqtt_password_secure = "LedLifetime123";

// HiveMQ Public Configuration (Non-Secure)
const char *mqtt_server_public = "broker.hivemq.com";
const int mqtt_port_public = 1883;
const char *mqtt_user_public = "";
const char *mqtt_password_public = "";

// Active configuration (will be set based on use_secure_mqtt)
const char *mqtt_server;
int mqtt_port;
const char *mqtt_user;
const char *mqtt_password;
const char *mqtt_client_id = "ESP32_LedPredict"; // Will be made unique in code

// MQTT Topics (Simplified)
const char *topic_data = "ledpredict/data";     // Data sensor utama
const char *topic_command = "ledpredict/cmd";   // Command untuk kontrol
const char *topic_status = "ledpredict/status"; // Status dan test messages
const char *topic_thermal = "ledpredict/thermal"; // AMG8833 thermal data
const char *topic_spectral = "ledpredict/spectral"; // AS7341 spectral data

// AMG8833 Configuration
#define AMG_COLS 8
#define AMG_ROWS 8
float pixels[AMG_COLS * AMG_ROWS]; // 64 thermal pixels

// AS7341 Configuration
uint16_t spectralReadings[12]; // 11 spectral channels + flicker

// Timing Configuration
unsigned long interval = 600000; // Normal: 10 minutes (600,000 ms)
unsigned long previousMillis = 0;
const unsigned long MIN_INTERVAL = 5000;    // Minimum 5 seconds
const unsigned long MAX_INTERVAL = 3600000; // Maximum 1 hour

// Status tracking
bool dhtStatus = false;
bool bh1750Status = false;
bool ina219Status = false;
bool amgStatus = false;
bool as7341Status = false;
bool wifiStatus = false;
bool mqttStatus = false;

// Testing counter
unsigned long dataCounter = 0;

// Sensor check configuration
const int MAX_SENSOR_CHECKS = 5;
int sensorCheckCount = 0;
bool sensorCheckComplete = false;

// NTP Configuration
const char* ntpServer = "pool.ntp.org";
const long gmtOffset_sec = 7 * 3600; // WIB = UTC+7
const int daylightOffset_sec = 0; // No daylight saving in Indonesia
bool ntpInitialized = false;

// Function declarations
void configureMQTT();
void sendToMQTT(float temp, float hum, float lux, float voltage, float current);
void testMQTTPublish(); // Test function
void mqttCallback(char* topic, byte* payload, unsigned int length);
void processCommand(String command);
void setInterval(unsigned long newInterval);
void sendResponse(String message, bool success = true);
bool connectToMQTT();
bool verifyMQTTConnection();
void reconnectMQTT();
bool verifyWiFiConnection();
bool verifyDHT22();
bool verifyBH1750();
bool verifyINA219();
bool verifyAMG8833();
bool verifyAS7341();
void printSystemStatus();
void reconnectWiFi();
void checkPowerSupply();
void initializeNTP();
String getCurrentTime();
String getTimestamp();
void resetDHT22();
void testDHT22Simple();
void sendThermalData();
String getThermalJSON();
float getAverageTemp();
float getMaxTemp();
float getMinTemp();
bool detectPresence();
void sendSpectralData();
String getSpectralJSON();
float calculateCCT();
void setAS7341LED(int current_ma);

void setup()
{
  Serial.begin(115200);
  Serial.setDebugOutput(true);

  // Configure MQTT settings based on TLS preference
  configureMQTT();

  // Set MQTT buffer size for larger payloads
  mqttClient.setBufferSize(512); // Increase buffer size

  Serial.println("\n🚀 LED Predict Starting...");
  Serial.printf("📡 MQTT: %s:%d (%s)\n", mqtt_server, mqtt_port,
                use_secure_mqtt ? "Secure" : "Public");
  Serial.printf("⏱️  Interval: %ds\n", interval/1000);

  // Initialize I2C
  Wire.begin(SDA_PIN, SCL_PIN);
  Wire.setClock(100000);
  delay(100);

  // Initialize sensors
  Serial.print("🔧 Sensors: ");

  // Initialize DHT22 with internal pull-up (no external resistor needed)
  Serial.print("🌡️ DHT22: ");

  // Enable internal pull-up resistor
  pinMode(DHTPIN, INPUT_PULLUP);
  delay(100);

  dht.begin();
  delay(3000); // Longer delay for DHT22 stabilization

  Serial.println("Using internal pull-up resistor");

  // Run simple test first
  testDHT22Simple();

  // Initial sensor status (will be verified in loop)
  Serial.println("Sensors will be verified during first readings...");

  // Initialize connections
  Serial.print("📶 WiFi: ");
  wifiStatus = verifyWiFiConnection();
  Serial.println(wifiStatus ? "Connected" : "Failed");

  if (wifiStatus) {
    Serial.print("📡 MQTT: ");
    mqttClient.setServer(mqtt_server, mqtt_port);
    mqttClient.setCallback(mqttCallback);
    mqttStatus = connectToMQTT();
    Serial.println(mqttStatus ? "Connected" : "Failed");
  }

  Serial.printf("🔧 Sensor verification: Will check sensors for first %d readings\n", MAX_SENSOR_CHECKS);

  // Initialize NTP if WiFi is connected
  if (wifiStatus) {
    Serial.print("🕐 NTP: ");
    initializeNTP();
  }

  Serial.println("✅ Ready!\n");
}

void loop()
{
  // Handle MQTT client loop - CRITICAL for MQTT to work
  if (mqttStatus && mqttClient.connected()) {
    mqttClient.loop();
  } else if (mqttStatus && !mqttClient.connected()) {
    Serial.println("MQTT disconnected in loop, attempting reconnect...");
    reconnectMQTT();
  }

  unsigned long currentMillis = millis();

  // Show countdown every 5 seconds (only when close to next reading)
  static unsigned long lastCountdown = 0;
  unsigned long timeToNext = interval - (currentMillis - previousMillis);

  if (timeToNext <= 15000 && currentMillis - lastCountdown >= 5000) { // Show countdown in last 15 seconds
    lastCountdown = currentMillis;
    if (timeToNext > 5000) {
      Serial.printf("⏳ Next sensor reading in %.0f seconds...\n", timeToNext/1000.0);
    }
  }

  if (currentMillis - previousMillis >= interval)
  {
    previousMillis = currentMillis;
    dataCounter++;

    // Simple header for data-only mode
    if (sensorCheckComplete) {
      Serial.printf("\n[%lu] ", dataCounter);
    } else {
      Serial.printf("\n=== Sensor Reading Cycle #%lu ===\n", dataCounter);
      Serial.printf("Time: %lu ms (%.1f seconds since boot)\n", millis(), millis()/1000.0);
    }

    // Verify connections
    if (!verifyWiFiConnection()) {
      if (!sensorCheckComplete) Serial.println("WiFi verification failed. Attempting reconnection...");
      reconnectWiFi();
      wifiStatus = verifyWiFiConnection();
    }

    if (wifiStatus && !verifyMQTTConnection()) {
      if (!sensorCheckComplete) Serial.println("MQTT verification failed. Attempting reconnection...");
      reconnectMQTT();
      mqttStatus = verifyMQTTConnection();
    }

    // Verify sensors (only during check phase)
    if (!sensorCheckComplete) {
      Serial.println("🔍 Verifying sensors...");
      if (!verifyDHT22()) {
        Serial.println("❌ DHT22 verification failed!");
        dhtStatus = false;
      } else {
        Serial.println("✅ DHT22 verified");
        dhtStatus = true;
      }

      Serial.println();

      if (!verifyBH1750()) {
        Serial.println("❌ BH1750 verification failed!");
        bh1750Status = false;
      } else {
        Serial.println("✅ BH1750 verified");
        bh1750Status = true;
      }

      Serial.println();

      if (!verifyINA219()) {
        Serial.println("❌ INA219 verification failed!");
        ina219Status = false;
      } else {
        Serial.println("✅ INA219 verified");
        ina219Status = true;
      }

      Serial.println();

      if (!verifyAMG8833()) {
        Serial.println("❌ AMG8833 verification failed!");
        amgStatus = false;
      } else {
        Serial.println("✅ AMG8833 verified");
        amgStatus = true;
      }

      Serial.println();

      if (!verifyAS7341()) {
        Serial.println("❌ AS7341 verification failed!");
        as7341Status = false;
      } else {
        Serial.println("✅ AS7341 verified");
        as7341Status = true;
      }

      Serial.println();
    }

    // Read sensors
    if (!sensorCheckComplete) {
      Serial.printf("\n🔍 Sensor Check #%d/%d: ", sensorCheckCount + 1, MAX_SENSOR_CHECKS);
    } else {
      Serial.printf("\n📊 Reading #%lu: ", dataCounter + 1);
    }
    float temp = NAN, hum = NAN, lux = -1, voltage = 0, current = 0;

    // DHT22 Temperature & Humidity
    if (dhtStatus || !sensorCheckComplete) {
      // Ensure internal pull-up is active
      pinMode(DHTPIN, INPUT_PULLUP);
      delay(50); // Longer delay for DHT22 without external pull-up

      temp = dht.readTemperature();
      hum = dht.readHumidity();

      if (isnan(temp) || isnan(hum) || temp < -40 || temp > 125 || hum < 0 || hum > 100) {
        temp = NAN;
        hum = NAN;
        if (!sensorCheckComplete) {
          dhtStatus = false;
          Serial.print("DHT22✗ ");
        } else {
          Serial.print("DHT22(N/A) ");
        }
      } else {
        if (!sensorCheckComplete) {
          dhtStatus = true;
        }
        Serial.printf("DHT22(%.1f°C,%.1f%%) ", temp, hum);
      }
    } else {
      temp = NAN;
      hum = NAN;
      Serial.print("DHT22(N/A) ");
    }

    // BH1750 Light Sensor
    if (bh1750Status || !sensorCheckComplete) {
      lux = lightMeter.readLightLevel();
      if (lux < 0 || lux > 100000) {
        lux = -1;
        if (!sensorCheckComplete) {
          bh1750Status = false;
          Serial.print("BH1750✗ ");
        } else {
          Serial.print("BH1750(N/A) ");
        }
      } else {
        if (!sensorCheckComplete) {
          bh1750Status = true;
        }
        Serial.printf("BH1750(%.0flux) ", lux);
      }
    } else {
      lux = -1;
      Serial.print("BH1750(N/A) ");
    }

    // INA219 Power Monitor
    if (ina219Status || !sensorCheckComplete) {
      voltage = ina219.getBusVoltage_V();
      current = ina219.getCurrent_mA();

      if (voltage < 0 || voltage > 32 || current < -3200 || current > 3200) {
        voltage = 0;
        current = 0;
        if (!sensorCheckComplete) {
          ina219Status = false;
          Serial.print("INA219✗");
        } else {
          Serial.print("INA219(N/A)");
        }
      } else {
        if (!sensorCheckComplete) {
          ina219Status = true;
        }
        Serial.printf("INA219(%.2fV,%.0fmA)", voltage, current);
      }
    } else {
      voltage = 0;
      current = 0;
      Serial.print("INA219(N/A)");
    }
    Serial.println();

    // Update sensor check counter
    if (!sensorCheckComplete) {
      sensorCheckCount++;
      if (sensorCheckCount >= MAX_SENSOR_CHECKS) {
        sensorCheckComplete = true;
        Serial.printf("\n✅ Sensor check complete. Final: DHT22:%s BH1750:%s INA219:%s AMG8833:%s AS7341:%s\n",
                     dhtStatus ? "✓" : "✗",
                     bh1750Status ? "✓" : "✗",
                     ina219Status ? "✓" : "✗",
                     amgStatus ? "✓" : "✗",
                     as7341Status ? "✓" : "✗");
        Serial.println("📊 Switching to simple monitoring mode\n");
      }
    }

    // Send data if available
    int workingSensors = (dhtStatus ? 1 : 0) + (bh1750Status ? 1 : 0) + (ina219Status ? 1 : 0) + (amgStatus ? 1 : 0) + (as7341Status ? 1 : 0);
    Serial.printf("📊 Sensors: %d/5 working ", workingSensors);

    // Show detailed data only during sensor check phase
    if (!sensorCheckComplete) {
      if (dhtStatus && !isnan(temp)) {
        Serial.printf("│ 🌡️  Temperature: %8.1f°C          │\n", temp);
      } else {
        Serial.printf("│ 🌡️  Temperature: %8s            │\n", "FAILED");
      }

      if (dhtStatus && !isnan(hum)) {
        Serial.printf("│ 💧 Humidity:    %8.1f%%           │\n", hum);
      } else {
        Serial.printf("│ 💧 Humidity:    %8s            │\n", "FAILED");
      }

    if (bh1750Status && lux >= 0) {
      Serial.printf("│ 💡 Light Level: %8.1f lux        │\n", lux);
    } else {
      Serial.printf("│ 💡 Light Level: %8s            │\n", "FAILED");
    }

    if (ina219Status) {
      Serial.printf("│ ⚡ Voltage:     %8.3fV           │\n", voltage);
      Serial.printf("│ 🔌 Current:     %8.1fmA          │\n", current);
      Serial.printf("│ 🔋 Power:       %8.2fmW          │\n", voltage * current);
    } else {
      Serial.printf("│ ⚡ Voltage:     %8s            │\n", "FAILED");
      Serial.printf("│ � Current:     %8s            │\n", "FAILED");
      Serial.printf("│ �� Power:       %8s            │\n", "FAILED");
    }

    // AMG8833 Thermal data
    if (amgStatus) {
      // Read thermal data using SparkFun library
      for(int i = 0; i < AMG_COLS * AMG_ROWS; i++) {
        pixels[i] = amg.getPixelTemperature(i);
      }
      float avgTemp = getAverageTemp();
      float maxTemp = getMaxTemp();
      bool presence = detectPresence();
      Serial.printf("│ 🌡️  Thermal Avg: %8.1f°C          │\n", avgTemp);
      Serial.printf("│ 🔥 Thermal Max: %8.1f°C          │\n", maxTemp);
      Serial.printf("│ 👤 Presence:    %8s            │\n", presence ? "DETECTED" : "NONE");
    } else {
      Serial.printf("│ 🌡️  Thermal Avg: %8s            │\n", "FAILED");
      Serial.printf("│ 🔥 Thermal Max: %8s            │\n", "FAILED");
      Serial.printf("│ 👤 Presence:    %8s            │\n", "FAILED");
    }

    // AS7341 Spectral data
    if (as7341Status) {
      // Read spectral data
      if (as7341.readAllChannels(spectralReadings)) {
        float cct = calculateCCT();
        uint16_t clear = spectralReadings[AS7341_CHANNEL_CLEAR];
        uint16_t nir = spectralReadings[AS7341_CHANNEL_NIR];
        Serial.printf("│ 🌈 Color Temp:  %8.0fK          │\n", cct);
        Serial.printf("│ ☀️  Clear:       %8d            │\n", clear);
        Serial.printf("│ 🔴 NIR:         %8d            │\n", nir);
      } else {
        Serial.printf("│ 🌈 Color Temp:  %8s            │\n", "FAILED");
        Serial.printf("│ ☀️  Clear:       %8s            │\n", "FAILED");
        Serial.printf("│ 🔴 NIR:         %8s            │\n", "FAILED");
      }
    } else {
      Serial.printf("│ 🌈 Color Temp:  %8s            │\n", "FAILED");
      Serial.printf("│ ☀️  Clear:       %8s            │\n", "FAILED");
      Serial.printf("│ 🔴 NIR:         %8s            │\n", "FAILED");
    }

      Serial.println("└─────────────────────────────────────────┘");
    }



    // Send data
    if (wifiStatus && mqttStatus) {
      bool hasValidData = bh1750Status || ina219Status || dhtStatus;
      if (hasValidData) {
        if (sensorCheckComplete) {
          Serial.print("📤 Sending... ");
        } else {
          Serial.println("📤 Sending data (some sensors may have failed)");
        }
        sendToMQTT(temp, hum, lux, voltage, current);

        // Send thermal data separately if AMG8833 is working
        if (amgStatus) {
          sendThermalData();
        }

        // Send spectral data separately if AS7341 is working
        if (as7341Status) {
          sendSpectralData();
        }

        if (sensorCheckComplete) {
          Serial.println("✅");
        }
      } else {
        Serial.println("❌ All sensors failed - No data sent");
      }
    } else {
      Serial.printf("❌ Connection failed - WiFi:%s MQTT:%s\n",
                   wifiStatus ? "✓" : "✗", mqttStatus ? "✓" : "✗");
    }
    
    // Simple status for data-only mode
    if (sensorCheckComplete) {
      Serial.printf("📶 WiFi:%s 📡 MQTT:%s 🕐 %s ⏰ Next in %ds\n",
                   wifiStatus ? "✓" : "✗",
                   mqttStatus ? "✓" : "✗",
                   ntpInitialized ? getCurrentTime().c_str() : "No time",
                   interval/1000);
    } else {
      printSystemStatus();
      unsigned long nextReading = millis() + interval;
      Serial.printf("\n⏰ Next reading in %.1f seconds (at %lu ms)\n", interval/1000.0, nextReading);
      Serial.println("═══════════════════════════════════════════════════════════");
    }
  }
  
  // Small delay to prevent watchdog issues
  delay(100);
}

void sendToMQTT(float temp, float hum, float lux, float voltage, float current)
{
  if (sensorCheckComplete) {
    Serial.print("📤 Sending... ");
  } else {
    Serial.println("\n=== Sending Data to MQTT ===");
  }

  if (!verifyMQTTConnection()) {
    Serial.println("MQTT not available, cannot send data!");
    return;
  }

  // Create compact JSON payload
  StaticJsonDocument<256> doc; // Reduced size
  doc["ts"] = millis(); // Shorter key
  doc["dt"] = getTimestamp(); // Shorter key
  doc["cnt"] = dataCounter; // Shorter key

  // Handle NaN values for temperature and humidity
  if (!isnan(temp)) {
    doc["temp"] = round(temp * 10) / 10.0; // Shorter key
  }

  if (!isnan(hum)) {
    doc["hum"] = round(hum * 10) / 10.0; // Shorter key
  }

  if (lux >= 0) {
    doc["lux"] = round(lux * 10) / 10.0; // Only if valid
  }

  doc["volt"] = round(voltage * 1000) / 1000.0; // Shorter key
  doc["curr"] = round(current * 10) / 10.0; // Shorter key
  doc["dev"] = mqtt_client_id; // Shorter key
  doc["int"] = interval; // Shorter key

  // Compact sensor status
  doc["dht22"] = dhtStatus;
  doc["bh1750"] = bh1750Status;
  doc["ina219"] = ina219Status;
  doc["amg8833"] = amgStatus;
  doc["as7341"] = as7341Status;

  String jsonString;
  serializeJson(doc, jsonString);

  // Show detailed info only during sensor check phase
  if (!sensorCheckComplete) {
    Serial.printf("Sending sensor data (Transmission #%lu):\n", dataCounter);
    Serial.printf("Temperature: %.1f°C\n", temp);
    Serial.printf("Humidity: %.1f%%\n", hum);
    Serial.printf("Light Level: %.1f lux\n", lux);
    Serial.printf("Voltage: %.3fV\n", voltage);
    Serial.printf("Current: %.1fmA\n", current);
    Serial.printf("JSON: %s\n", jsonString.c_str());
  }

  // Check connection
  if (!mqttClient.connected()) {
    if (!sensorCheckComplete) Serial.println("MQTT disconnected, reconnecting...");
    reconnectMQTT();
    if (!mqttClient.connected()) {
      if (sensorCheckComplete) {
        Serial.println("❌ MQTT failed");
      } else {
        Serial.println("✗ Reconnect failed, skipping publish");
      }
      return;
    }
  }

  // Publish with smaller payload and no retain
  bool result = mqttClient.publish(topic_data, jsonString.c_str(), false); // no retain

  if (sensorCheckComplete) {
    Serial.println(result ? "✅" : "❌");
  } else {
    if (result) {
      Serial.println("✓ Data sent successfully to MQTT!");
    } else {
      Serial.println("✗ Failed to send data to MQTT!");
      Serial.printf("MQTT State: %d\n", mqttClient.state());
      Serial.printf("Payload size: %d bytes\n", jsonString.length());
    }
  }
}

// WiFi verification function
bool verifyWiFiConnection() {
  Serial.println("\nVerifying WiFi connection...");
  
  if (WiFi.status() == WL_CONNECTED) {
    Serial.printf("WiFi already connected to: %s\n", WiFi.SSID().c_str());
    Serial.printf("IP Address: %s\n", WiFi.localIP().toString().c_str());
    Serial.printf("Signal Strength: %d dBm\n", WiFi.RSSI());
    return true;
  }
  
  Serial.printf("Connecting to WiFi network: %s\n", ssid);
  WiFi.begin(ssid, password);
  
  unsigned long startTime = millis();
  int dots = 0;
  
  while (WiFi.status() != WL_CONNECTED && millis() - startTime < 20000) {
    delay(500);
    Serial.print(".");
    dots++;
    if (dots % 10 == 0) Serial.println();
  }
  
  if (WiFi.status() == WL_CONNECTED) {
    Serial.println("\nWiFi connected successfully!");
    Serial.printf("SSID: %s\n", WiFi.SSID().c_str());
    Serial.printf("IP Address: %s\n", WiFi.localIP().toString().c_str());
    Serial.printf("Gateway: %s\n", WiFi.gatewayIP().toString().c_str());
    Serial.printf("DNS: %s\n", WiFi.dnsIP().toString().c_str());
    Serial.printf("Signal Strength: %d dBm\n", WiFi.RSSI());
    Serial.printf("MAC Address: %s\n", WiFi.macAddress().c_str());
    return true;
  } else {
    Serial.println("\nWiFi connection failed!");
    Serial.printf("WiFi Status: %d\n", WiFi.status());
    return false;
  }
}

// DHT22 verification function - With internal pull-up
bool verifyDHT22() {
  Serial.println("🔍 Testing DHT22 with internal pull-up...");

  // Ensure internal pull-up is enabled
  pinMode(DHTPIN, INPUT_PULLUP);
  delay(100);

  // Re-initialize DHT
  dht.begin();
  delay(2000);

  // Test multiple times with proper delays
  for (int attempt = 1; attempt <= 5; attempt++) {
    Serial.printf("Attempt %d: ", attempt);

    float temp = dht.readTemperature();
    float hum = dht.readHumidity();

    Serial.printf("T=%.1f°C H=%.1f%% ", temp, hum);

    // Check if readings are valid
    if (!isnan(temp) && !isnan(hum)) {
      // DHT22 range check
      if (temp > -50 && temp < 150 && hum >= 0 && hum <= 100) {
        Serial.println("✅ SUCCESS");
        return true;
      } else {
        Serial.println("❌ Out of range");
      }
    } else {
      Serial.println("❌ NaN values");
    }

    // Longer delay for DHT22 without external pull-up
    delay(3000);
  }

  Serial.println("\n❌ DHT22 FAILED - Troubleshooting:");
  Serial.println("1. Check wiring: VCC→3.3V, GND→GND, DATA→GPIO4");
  Serial.println("2. Verify sensor type (DHT22 not DHT11)");
  Serial.println("3. Try external 4.7kΩ pull-up resistor");
  Serial.println("4. Check power supply stability");
  Serial.printf("5. Internal pull-up enabled on GPIO%d\n", DHTPIN);

  return false;
}

// BH1750 verification function
bool verifyBH1750() {
  Serial.println("Verifying BH1750 light sensor...");
  
  // Check if BH1750 is present on I2C bus (address 0x23 or 0x5C)
  Wire.beginTransmission(0x23);
  if (Wire.endTransmission() != 0) {
    Wire.beginTransmission(0x5C);
    if (Wire.endTransmission() != 0) {
      Serial.println("BH1750 not found on I2C bus!");
      return false;
    } else {
      Serial.println("BH1750 found at address 0x5C");
    }
  } else {
    Serial.println("BH1750 found at address 0x23");
  }
  
  if (!lightMeter.begin(BH1750::CONTINUOUS_HIGH_RES_MODE)) {
    Serial.println("BH1750 initialization failed!");
    
    // Try different modes
    Serial.println("Trying different BH1750 modes...");
    if (lightMeter.begin(BH1750::ONE_TIME_HIGH_RES_MODE)) {
      Serial.println("BH1750 initialized in ONE_TIME_HIGH_RES_MODE");
    } else if (lightMeter.begin(BH1750::CONTINUOUS_LOW_RES_MODE)) {
      Serial.println("BH1750 initialized in CONTINUOUS_LOW_RES_MODE");
    } else {
      Serial.println("All BH1750 initialization modes failed!");
      return false;
    }
  }
  
  // Give sensor time to stabilize
  delay(500);
  
  // Try to read light level multiple times
  for (int i = 0; i < 5; i++) {
    float lux = lightMeter.readLightLevel();
    
    if (lux >= 0 && lux <= 100000) {
      Serial.printf("BH1750 verified - Light Level: %.1f lux\n", lux);
      return true;
    }
    
    Serial.printf("BH1750 attempt %d failed (reading: %.1f), retrying...\n", i + 1, lux);
    delay(1000);
  }
  
  Serial.println("BH1750 verification failed after 5 attempts!");
  return false;
}

// INA219 verification function
bool verifyINA219() {
  Serial.println("Verifying INA219 current sensor...");
  
  // Check if INA219 is present on I2C bus (address 0x40, 0x41, 0x44, or 0x45)
  byte ina219_addresses[] = {0x40, 0x41, 0x44, 0x45};
  bool found = false;
  byte foundAddress = 0x40; // Default address
  
  for (int i = 0; i < 4; i++) {
    Wire.beginTransmission(ina219_addresses[i]);
    if (Wire.endTransmission() == 0) {
      Serial.printf("INA219 found at address 0x%02X\n", ina219_addresses[i]);
      found = true;
      foundAddress = ina219_addresses[i];
      break;
    }
  }
  
  if (!found) {
    Serial.println("INA219 not found on I2C bus!");
    return false;
  }
  
  // Initialize INA219 (it will use the default address 0x40)
  if (!ina219.begin(&Wire)) {
    Serial.println("INA219 initialization failed!");
    return false;
  }
  
  // Configure INA219 for better accuracy
  ina219.setCalibration_32V_2A(); // Set calibration for 32V, 2A range
  
  // Give sensor time to stabilize
  delay(500);
  
  // Try to read voltage and current multiple times
  for (int i = 0; i < 5; i++) {
    float voltage = ina219.getBusVoltage_V();
    float current = ina219.getCurrent_mA();
    float shuntVoltage = ina219.getShuntVoltage_mV();
    
    // Check if readings are within reasonable range
    if (voltage >= 0 && voltage <= 32 && current >= -3200 && current <= 3200) {
      Serial.printf("INA219 verified - Bus Voltage: %.3fV, Current: %.1fmA, Shunt: %.3fmV\n", 
                   voltage, current, shuntVoltage);
      Serial.printf("INA219 operating at address 0x%02X\n", foundAddress);
      return true;
    }
    
    Serial.printf("INA219 attempt %d failed (V: %.3f, I: %.1f), retrying...\n", i + 1, voltage, current);
    delay(1000);
  }
  
  Serial.println("INA219 verification failed after 5 attempts!");
  return false;
}

// AMG8833 verification function
bool verifyAMG8833() {
  Serial.println("🔍 Testing AMG8833 thermal sensor...");

  // SparkFun library begin() doesn't return bool, so we test differently
  amg.begin();
  delay(100);

  // Test if sensor is responding by reading device temperature
  float deviceTemp = amg.getDeviceTemperature();
  if (isnan(deviceTemp) || deviceTemp < -40 || deviceTemp > 85) {
    Serial.println("❌ AMG8833 not found!");
    Serial.println("Check connections:");
    Serial.println("- VCC → 3.3V");
    Serial.println("- GND → GND");
    Serial.println("- SDA → GPIO21");
    Serial.println("- SCL → GPIO22");
    return false;
  }

  // Test reading thermal data
  delay(100); // Let sensor stabilize

  // Read thermal data using SparkFun library
  for(int i = 0; i < AMG_COLS * AMG_ROWS; i++) {
    pixels[i] = amg.getPixelTemperature(i);
  }

  // Check if we got valid data
  bool validData = false;
  for(int i = 0; i < AMG_COLS * AMG_ROWS; i++) {
    if (!isnan(pixels[i]) && pixels[i] > 0 && pixels[i] < 100) {
      validData = true;
      break;
    }
  }

  if (validData) {
    float avgTemp = getAverageTemp();
    Serial.printf("✅ AMG8833 verified - Avg temp: %.1f°C\n", avgTemp);
    return true;
  } else {
    Serial.println("❌ AMG8833 returning invalid data");
    return false;
  }
}

// AS7341 verification function
bool verifyAS7341() {
  Serial.println("🔍 Testing AS7341 spectral sensor...");

  if (!as7341.begin()) {
    Serial.println("❌ AS7341 not found!");
    Serial.println("Check connections:");
    Serial.println("- VIN → 3.3V");
    Serial.println("- GND → GND");
    Serial.println("- SDA → GPIO21");
    Serial.println("- SCL → GPIO22");
    return false;
  }

  // Configure sensor settings
  as7341.setATIME(100);  // Integration time
  as7341.setASTEP(999);  // Step count
  as7341.setGain(AS7341_GAIN_4X);  // 4X gain

  // Test reading spectral data
  delay(100);
  if (as7341.readAllChannels(spectralReadings)) {
    // Check if we got valid data
    bool validData = false;
    for(int i = 0; i < 12; i++) {
      if (spectralReadings[i] > 0) {
        validData = true;
        break;
      }
    }

    if (validData) {
      uint16_t clear = spectralReadings[AS7341_CHANNEL_CLEAR];
      Serial.printf("✅ AS7341 verified - Clear: %d\n", clear);
      return true;
    } else {
      Serial.println("❌ AS7341 returning zero values");
      return false;
    }
  } else {
    Serial.println("❌ AS7341 read failed");
    return false;
  }
}

// System status display function
void printSystemStatus() {
  Serial.println("\n=== System Status ===");
  Serial.printf("WiFi: %s\n", wifiStatus ? "✓ Connected" : "✗ Disconnected");
  if (wifiStatus) {
    Serial.printf("  IP: %s\n", WiFi.localIP().toString().c_str());
    Serial.printf("  RSSI: %d dBm\n", WiFi.RSSI());
  }
  Serial.printf("MQTT: %s\n", mqttStatus ? "✓ Connected" : "✗ Disconnected");
  if (mqttStatus) {
    Serial.printf("  Mode: %s\n", use_secure_mqtt ? "Secure (HiveMQ Cloud)" : "Non-Secure (HiveMQ Public)");
    Serial.printf("  Broker: %s:%d\n", mqtt_server, mqtt_port);
    Serial.printf("  Client: %s\n", mqtt_client_id);
    if (strlen(mqtt_user) > 0) {
      Serial.printf("  User: %s\n", mqtt_user);
    }
  }
  Serial.printf("DHT22: %s\n", dhtStatus ? "✓ Working" : "✗ Failed");
  Serial.printf("BH1750: %s\n", bh1750Status ? "✓ Working" : "✗ Failed");
  Serial.printf("INA219: %s\n", ina219Status ? "✓ Working" : "✗ Failed");
  Serial.printf("AMG8833: %s\n", amgStatus ? "✓ Working" : "✗ Failed");
  Serial.printf("AS7341: %s\n", as7341Status ? "✓ Working" : "✗ Failed");
  Serial.printf("Interval: %lu ms (%.1fs)\n", interval, interval/1000.0);
  Serial.printf("Data Sent: %lu transmissions\n", dataCounter);
  Serial.printf("Free Heap: %d bytes\n", ESP.getFreeHeap());
  Serial.printf("Uptime: %lu seconds\n", millis() / 1000);
  Serial.println("====================");
}

// WiFi reconnection function
void reconnectWiFi() {
  Serial.println("Attempting WiFi reconnection...");
  WiFi.disconnect();
  delay(1000);
  WiFi.begin(ssid, password);
  
  unsigned long startTime = millis();
  while (WiFi.status() != WL_CONNECTED && millis() - startTime < 15000) {
    delay(500);
    Serial.print(".");
  }
  
  if (WiFi.status() == WL_CONNECTED) {
    Serial.println("\nWiFi reconnected successfully!");
    wifiStatus = true;
  } else {
    Serial.println("\nWiFi reconnection failed!");
    wifiStatus = false;
  }
}

// Power supply check function
void checkPowerSupply() {
  Serial.println("\n=== Power Supply Check ===");

  Serial.println("Checking 3.3V rail stability...");
  Serial.println("Verifying power to sensors:");
  Serial.println("- DHT22 requires stable 3.3V on VCC pin");
  Serial.println("- BH1750 requires stable 3.3V on VCC pin");
  Serial.println("- INA219 requires stable 3.3V on VIN pin");
  Serial.println("- All sensors require common GND connection");

  Serial.printf("Free heap: %d bytes (should be >200KB for stable operation)\n", ESP.getFreeHeap());

  if (ESP.getFreeHeap() < 200000) {
    Serial.println("⚠ Warning: Low memory detected!");
  }

  Serial.println("=========================");
}

// Test MQTT publish function
void testMQTTPublish() {
  Serial.println("\n🧪 MQTT Publish Test:");

  if (!mqttClient.connected()) {
    Serial.println("❌ MQTT not connected");
    reconnectMQTT();
    if (!mqttClient.connected()) {
      Serial.println("❌ Reconnect failed");
      return;
    }
  }

  // Simple test message
  String testMsg = "{\"test\":\"ok\",\"ts\":" + String(millis()) + "}";

  Serial.printf("Topic: %s\n", topic_data);
  Serial.printf("Message: %s\n", testMsg.c_str());
  Serial.printf("Size: %d bytes\n", testMsg.length());

  bool result = mqttClient.publish(topic_data, testMsg.c_str(), false);
  Serial.printf("Result: %s\n", result ? "✅ SUCCESS" : "❌ FAILED");

  if (!result) {
    Serial.printf("MQTT State: %d\n", mqttClient.state());
    Serial.printf("Buffer Size: %d\n", mqttClient.getBufferSize());
  }
}

// Configure MQTT settings based on security preference
void configureMQTT() {
  if (use_secure_mqtt) {
    // HiveMQ Cloud with TLS/SSL (Secure)
    mqtt_server = mqtt_server_secure;
    mqtt_port = mqtt_port_secure;
    mqtt_user = mqtt_user_secure;
    mqtt_password = mqtt_password_secure;

    // Configure secure client with certificate verification
    espClientSecure.setCACert(hivemq_cloud_ca);
    espClientSecure.setTimeout(10);
    mqttClient.setClient(espClientSecure);

    Serial.println("MQTT Mode: HiveMQ Cloud (Secure - TLS with Certificate)");
  } else {
    // HiveMQ Public without TLS (Non-Secure)
    mqtt_server = mqtt_server_public;
    mqtt_port = mqtt_port_public;
    mqtt_user = mqtt_user_public;
    mqtt_password = mqtt_password_public;

    // Configure plain client
    mqttClient.setClient(espClientPlain);

    Serial.println("MQTT Mode: HiveMQ Public (Non-Secure - Plain Connection)");
  }

  Serial.printf("Broker: %s:%d\n", mqtt_server, mqtt_port);
  if (strlen(mqtt_user) > 0) {
    Serial.printf("Authentication: %s\n", mqtt_user);
  } else {
    Serial.println("Authentication: None (Public broker)");
  }
}

// MQTT callback function
void mqttCallback(char* topic, byte* payload, unsigned int length) {
  String message;
  for (int i = 0; i < length; i++) {
    message += (char)payload[i];
  }

  // Handle command messages
  if (String(topic) == topic_command) {
    Serial.printf("📥 MQTT Command: %s\n", message.c_str());
    processCommand(message);
  }
}

// Connect to MQTT broker
bool connectToMQTT() {
  Serial.println("\nConnecting to MQTT broker...");
  Serial.printf("Server: %s:%d (%s)\n", mqtt_server, mqtt_port, use_secure_mqtt ? "Secure" : "Non-Secure");
  Serial.printf("Client ID: %s\n", mqtt_client_id);
  if (strlen(mqtt_user) > 0) {
    Serial.printf("Username: %s\n", mqtt_user);
  }

  // Test DNS resolution and network connectivity
  Serial.println("Testing DNS resolution...");
  IPAddress serverIP;
  if (WiFi.hostByName(mqtt_server, serverIP)) {
    Serial.printf("✓ DNS resolution: %s -> %s\n", mqtt_server, serverIP.toString().c_str());
  } else {
    Serial.printf("✗ DNS resolution failed for: %s\n", mqtt_server);
    Serial.println("Check DNS settings or try different DNS server");
    return false;
  }

  Serial.println("Testing network connectivity...");
  WiFiClient testClient;
  if (testClient.connect(mqtt_server, mqtt_port)) {
    Serial.println("✓ Network connectivity to MQTT server: OK");
    testClient.stop();
  } else {
    Serial.println("✗ Network connectivity to MQTT server: FAILED");
    Serial.println("Check internet connection and firewall settings");
    return false;
  }

  // Generate unique client ID using MAC address
  String macAddress = WiFi.macAddress();
  macAddress.replace(":", "");
  String clientId = String(mqtt_client_id) + "_" + macAddress.substring(6); // Last 6 chars of MAC

  // Connect with retry mechanism
  Serial.println("Attempting MQTT connection...");
  Serial.printf("Using client ID: %s\n", clientId.c_str());

  bool connected = false;
  int attempts = 0;
  const int maxAttempts = 3;

  while (!connected && attempts < maxAttempts) {
    attempts++;
    Serial.printf("Connection attempt %d/%d...\n", attempts, maxAttempts);

    if (strlen(mqtt_user) > 0) {
      Serial.printf("Connecting with credentials: %s / %s\n", mqtt_user, mqtt_password);
      connected = mqttClient.connect(clientId.c_str(), mqtt_user, mqtt_password);
    } else {
      Serial.println("Connecting without credentials (public broker)");
      connected = mqttClient.connect(clientId.c_str());
    }

    if (!connected) {
      Serial.printf("Attempt %d failed, rc=%d\n", attempts, mqttClient.state());
      if (attempts < maxAttempts) {
        Serial.println("Waiting 2 seconds before retry...");
        delay(2000);
      }
    }
  }

  Serial.printf("Final connection result: %s\n", connected ? "SUCCESS" : "FAILED");

  if (connected) {
    Serial.println("✓ MQTT connected successfully!");

    // Subscribe to command topic
    mqttClient.subscribe(topic_command);
    Serial.printf("Subscribed to command topic: %s\n", topic_command);

    Serial.printf("Ready to publish data to: %s\n", topic_data);

    // Test publish to verify MQTT is working
    String testMsg = "{\"test\":\"connection_ok\",\"status\":\"mqtt_ready\",\"device\":\"" + String(clientId) + "\",\"timestamp\":" + String(millis()) + ",\"datetime\":\"" + getTimestamp() + "\"}";
    bool testResult = mqttClient.publish(topic_status, testMsg.c_str());
    Serial.printf("Test publish result: %s\n", testResult ? "SUCCESS" : "FAILED");
    if (testResult) {
      Serial.println("✓ MQTT publish test successful!");
    } else {
      Serial.println("✗ MQTT publish test failed!");
    }

    return true;
  } else {
    Serial.printf("✗ MQTT connection failed, rc=%d\n", mqttClient.state());

    // Print detailed error information
    switch(mqttClient.state()) {
      case -4: Serial.println("Error: Connection timeout"); break;
      case -3: Serial.println("Error: Connection lost"); break;
      case -2: Serial.println("Error: Connect failed"); break;
      case -1: Serial.println("Error: Disconnected"); break;
      case 1: Serial.println("Error: Bad protocol"); break;
      case 2: Serial.println("Error: Bad client ID"); break;
      case 3: Serial.println("Error: Unavailable"); break;
      case 4: Serial.println("Error: Bad credentials"); break;
      case 5: Serial.println("Error: Unauthorized"); break;
      default: Serial.println("Error: Unknown"); break;
    }

    Serial.println("Trying again in 5 seconds...");
    return false;
  }
}

// Verify MQTT connection
bool verifyMQTTConnection() {
  if (mqttClient.connected()) {
    // Additional check: try to loop to process any pending messages
    mqttClient.loop();
    return true;
  } else {
    if (!sensorCheckComplete) {
      Serial.println("MQTT connection lost!");
    }
    return false;
  }
}

// Reconnect to MQTT
void reconnectMQTT() {
  Serial.println("Attempting MQTT reconnection...");

  int attempts = 0;
  while (!mqttClient.connected() && attempts < 3) {
    if (connectToMQTT()) {
      mqttStatus = true;
      Serial.println("MQTT reconnected successfully!");
      break;
    } else {
      attempts++;
      Serial.printf("MQTT reconnection attempt %d failed\n", attempts);
      delay(5000);
    }
  }

  if (!mqttClient.connected()) {
    Serial.println("MQTT reconnection failed after 3 attempts!");
    mqttStatus = false;
  }
}

// Process MQTT commands
void processCommand(String command) {
  // Clean up the command string
  command.trim();

  StaticJsonDocument<200> doc;
  DeserializationError error = deserializeJson(doc, command);

  if (error) {
    Serial.printf("Failed to parse JSON: %s\n", error.c_str());
    Serial.printf("Received: %s\n", command.c_str());
    sendResponse("Invalid JSON format", false);
    return;
  }

  String action = doc["action"];

  if (action == "set_interval") {
    if (doc.containsKey("value") && doc.containsKey("unit")) {
      unsigned long value = doc["value"];
      String unit = doc["unit"];

      unsigned long newInterval;
      if (unit == "seconds" || unit == "s") {
        newInterval = value * 1000;
      } else if (unit == "minutes" || unit == "m") {
        newInterval = value * 60 * 1000;
      } else {
        sendResponse("Invalid unit. Use 'seconds', 's', 'minutes', or 'm'", false);
        return;
      }

      setInterval(newInterval);
    } else {
      sendResponse("Missing 'value' or 'unit' parameter", false);
    }
  }
  else if (action == "get_interval") {
    String response = "{\"action\":\"get_interval\",\"interval_ms\":" + String(interval) +
                     ",\"interval_seconds\":" + String(interval/1000) +
                     ",\"interval_minutes\":" + String(interval/60000) + "}";
    mqttClient.publish(topic_status, response.c_str());
  }
  else if (action == "get_status") {
    String response = "{\"action\":\"get_status\",\"wifi\":" + String(wifiStatus ? "true" : "false") +
                     ",\"mqtt\":" + String(mqttStatus ? "true" : "false") +
                     ",\"dht22\":" + String(dhtStatus ? "true" : "false") +
                     ",\"bh1750\":" + String(bh1750Status ? "true" : "false") +
                     ",\"ina219\":" + String(ina219Status ? "true" : "false") +
                     ",\"amg8833\":" + String(amgStatus ? "true" : "false") +
                     ",\"as7341\":" + String(as7341Status ? "true" : "false") +
                     ",\"uptime\":" + String(millis()/1000) +
                     ",\"free_heap\":" + String(ESP.getFreeHeap()) + "}";
    mqttClient.publish(topic_status, response.c_str());
  }
  else if (action == "force_reading") {
    Serial.println("Force reading requested via MQTT");
    previousMillis = 0; // Reset timer to trigger immediate reading
    sendResponse("Force reading triggered");
  }
  else if (action == "reset_dht22") {
    Serial.println("DHT22 reset requested via MQTT");
    resetDHT22();
    // Re-verify DHT22 after reset
    dhtStatus = verifyDHT22();
    String status = dhtStatus ? "success" : "failed";
    sendResponse("DHT22 reset " + status);
  }
  else if (action == "test_dht22") {
    Serial.println("DHT22 test requested via MQTT");
    testDHT22Simple();
    sendResponse("DHT22 test completed - check serial output");
  }
  else if (action == "test_mqtt") {
    Serial.println("MQTT test requested");
    testMQTTPublish();
    sendResponse("MQTT test completed - check serial output");
  }
  else if (action == "test_thermal") {
    Serial.println("Thermal sensor test requested");
    if (amgStatus) {
      sendThermalData();
      sendResponse("Thermal data sent - check thermal topic");
    } else {
      sendResponse("AMG8833 sensor not available", false);
    }
  }
  else if (action == "test_spectral") {
    Serial.println("Spectral sensor test requested");
    if (as7341Status) {
      sendSpectralData();
      sendResponse("Spectral data sent - check spectral topic");
    } else {
      sendResponse("AS7341 sensor not available", false);
    }
  }
  else if (action == "set_led") {
    if (doc.containsKey("current")) {
      int current = doc["current"];
      if (as7341Status) {
        setAS7341LED(current);
        sendResponse("AS7341 LED set to " + String(current) + "mA");
      } else {
        sendResponse("AS7341 sensor not available", false);
      }
    } else {
      sendResponse("Missing 'current' parameter", false);
    }
  }
  else {
    sendResponse("Unknown action: " + action, false);
  }
}

// Set new interval with validation
void setInterval(unsigned long newInterval) {
  if (newInterval < MIN_INTERVAL) {
    sendResponse("Interval too small. Minimum is " + String(MIN_INTERVAL/1000) + " seconds", false);
    return;
  }

  if (newInterval > MAX_INTERVAL) {
    sendResponse("Interval too large. Maximum is " + String(MAX_INTERVAL/60000) + " minutes", false);
    return;
  }

  unsigned long oldInterval = interval;
  interval = newInterval;

  Serial.printf("✅ Interval changed: %ds -> %ds\n", oldInterval/1000, interval/1000);

  String response = "{\"action\":\"set_interval\",\"old_interval\":" + String(oldInterval) +
                   ",\"new_interval\":" + String(interval) +
                   ",\"seconds\":" + String(interval/1000) +
                   ",\"minutes\":" + String(interval/60000) + "}";

  sendResponse(response);

  // Status update tidak perlu dikirim lagi karena sudah ada di response
}

// Send response message
void sendResponse(String message, bool success) {
  String response;
  if (message.startsWith("{")) {
    // Already JSON format
    response = message;
  } else {
    // Create JSON response
    response = "{\"success\":" + String(success ? "true" : "false") + ",\"message\":\"" + message + "\",\"timestamp\":" + String(millis()) + "}";
  }

  bool result = mqttClient.publish(topic_status, response.c_str());
  if (result) {
    Serial.println("Response sent: " + response);
  } else {
    Serial.println("Failed to send response");
  }
}

// NTP Functions
void initializeNTP() {
  configTime(gmtOffset_sec, daylightOffset_sec, ntpServer);

  // Wait for time to be set
  int attempts = 0;
  while (!time(nullptr) && attempts < 10) {
    delay(1000);
    attempts++;
  }

  if (time(nullptr)) {
    ntpInitialized = true;
    Serial.println("Synchronized ✓");
    Serial.printf("Current time: %s\n", getCurrentTime().c_str());
  } else {
    ntpInitialized = false;
    Serial.println("Failed ✗");
  }
}

String getCurrentTime() {
  if (!ntpInitialized) {
    return "Time not available";
  }

  time_t now;
  struct tm timeinfo;
  time(&now);
  localtime_r(&now, &timeinfo);

  char timeString[64];
  strftime(timeString, sizeof(timeString), "%Y-%m-%d %H:%M:%S WIB", &timeinfo);
  return String(timeString);
}

String getTimestamp() {
  if (!ntpInitialized) {
    return "N/A";
  }

  time_t now;
  struct tm timeinfo;
  time(&now);
  localtime_r(&now, &timeinfo);

  char timeString[32];
  strftime(timeString, sizeof(timeString), "%Y-%m-%d %H:%M:%S", &timeinfo);
  return String(timeString);
}

// DHT22 Reset Function with internal pull-up
void resetDHT22() {
  Serial.println("🔄 Resetting DHT22 (using internal pull-up)...");

  // Reset sequence for DHT22 without external pull-up
  pinMode(DHTPIN, OUTPUT);
  digitalWrite(DHTPIN, LOW);
  delay(50);  // Longer low pulse
  digitalWrite(DHTPIN, HIGH);
  delay(100); // Longer high pulse

  // Enable internal pull-up
  pinMode(DHTPIN, INPUT_PULLUP);
  delay(200); // Wait for stabilization

  // Re-initialize
  dht.begin();
  delay(3000); // Longer delay for DHT22

  Serial.println("DHT22 reset complete with internal pull-up");
}

// AMG8833 thermal data functions
float getAverageTemp() {
  float sum = 0;
  int validCount = 0;

  for(int i = 0; i < AMG_COLS * AMG_ROWS; i++) {
    if (!isnan(pixels[i])) {
      sum += pixels[i];
      validCount++;
    }
  }

  return validCount > 0 ? sum / validCount : 0;
}

float getMaxTemp() {
  float maxTemp = -999;

  for(int i = 0; i < AMG_COLS * AMG_ROWS; i++) {
    if (!isnan(pixels[i]) && pixels[i] > maxTemp) {
      maxTemp = pixels[i];
    }
  }

  return maxTemp == -999 ? 0 : maxTemp;
}

float getMinTemp() {
  float minTemp = 999;

  for(int i = 0; i < AMG_COLS * AMG_ROWS; i++) {
    if (!isnan(pixels[i]) && pixels[i] < minTemp) {
      minTemp = pixels[i];
    }
  }

  return minTemp == 999 ? 0 : minTemp;
}

bool detectPresence() {
  float avgTemp = getAverageTemp();
  float maxTemp = getMaxTemp();
  float tempDiff = maxTemp - avgTemp;

  // Presence detected if there's a significant temperature difference
  // indicating a warm object (like human body) in the field of view
  return tempDiff > 2.0; // 2°C difference threshold
}

String getThermalJSON() {
  StaticJsonDocument<1024> doc;

  doc["timestamp"] = millis();
  doc["datetime"] = getTimestamp();
  doc["device_id"] = mqtt_client_id;

  // Thermal statistics
  doc["avg_temp"] = round(getAverageTemp() * 10) / 10.0;
  doc["max_temp"] = round(getMaxTemp() * 10) / 10.0;
  doc["min_temp"] = round(getMinTemp() * 10) / 10.0;
  doc["presence"] = detectPresence();

  // 8x8 thermal array
  JsonArray thermal_array = doc.createNestedArray("thermal_data");
  for(int row = 0; row < AMG_ROWS; row++) {
    JsonArray row_array = thermal_array.createNestedArray();
    for(int col = 0; col < AMG_COLS; col++) {
      float temp = pixels[row * AMG_COLS + col];
      row_array.add(round(temp * 10) / 10.0);
    }
  }

  String jsonString;
  serializeJson(doc, jsonString);
  return jsonString;
}

void sendThermalData() {
  if (!amgStatus) {
    return; // Don't send if sensor not working
  }

  // Read thermal data using SparkFun library
  for(int i = 0; i < AMG_COLS * AMG_ROWS; i++) {
    pixels[i] = amg.getPixelTemperature(i);
  }

  // Generate JSON
  String thermalJSON = getThermalJSON();

  // Publish to MQTT
  if (mqttClient.connected()) {
    bool result = mqttClient.publish(topic_thermal, thermalJSON.c_str(), false);
    if (!sensorCheckComplete) {
      Serial.printf("Thermal data sent: %s\n", result ? "✅" : "❌");
    }
  }
}

// AS7341 spectral data functions
float calculateCCT() {
  // Simple CCT calculation using red and blue channels
  // This is a simplified calculation - for accurate CCT, more complex algorithms are needed
  uint16_t red = spectralReadings[AS7341_CHANNEL_630nm_F7];
  uint16_t blue = spectralReadings[AS7341_CHANNEL_445nm_F2];

  if (blue == 0) return 0;

  float ratio = (float)red / (float)blue;

  // Simplified CCT estimation (not scientifically accurate)
  if (ratio > 1.5) return 2700; // Warm white
  else if (ratio > 1.0) return 4000; // Neutral white
  else return 6500; // Cool white
}

void setAS7341LED(int current_ma) {
  if (!as7341Status) return;

  if (current_ma == 0) {
    as7341.enableLED(false);
  } else {
    as7341.setLEDCurrent(current_ma);
    as7341.enableLED(true);
  }
}

String getSpectralJSON() {
  StaticJsonDocument<512> doc;

  doc["timestamp"] = millis();
  doc["datetime"] = getTimestamp();
  doc["device_id"] = mqtt_client_id;

  // Spectral channels
  doc["415nm"] = spectralReadings[AS7341_CHANNEL_415nm_F1];  // Violet
  doc["445nm"] = spectralReadings[AS7341_CHANNEL_445nm_F2];  // Blue
  doc["480nm"] = spectralReadings[AS7341_CHANNEL_480nm_F3];  // Cyan
  doc["515nm"] = spectralReadings[AS7341_CHANNEL_515nm_F4];  // Green
  doc["555nm"] = spectralReadings[AS7341_CHANNEL_555nm_F5];  // Yellow-Green
  doc["590nm"] = spectralReadings[AS7341_CHANNEL_590nm_F6];  // Yellow
  doc["630nm"] = spectralReadings[AS7341_CHANNEL_630nm_F7];  // Orange
  doc["680nm"] = spectralReadings[AS7341_CHANNEL_680nm_F8];  // Red
  doc["clear"] = spectralReadings[AS7341_CHANNEL_CLEAR];     // Clear
  doc["nir"] = spectralReadings[AS7341_CHANNEL_NIR];         // Near Infrared

  // Calculated values
  doc["cct"] = calculateCCT();

  // Sensor settings
  JsonObject settings = doc.createNestedObject("settings");
  settings["atime"] = 100;
  settings["astep"] = 999;
  settings["gain"] = 4;

  String jsonString;
  serializeJson(doc, jsonString);
  return jsonString;
}

void sendSpectralData() {
  if (!as7341Status) {
    return; // Don't send if sensor not working
  }

  // Read spectral data
  if (!as7341.readAllChannels(spectralReadings)) {
    return; // Failed to read
  }

  // Generate JSON
  String spectralJSON = getSpectralJSON();

  // Publish to MQTT
  if (mqttClient.connected()) {
    bool result = mqttClient.publish(topic_spectral, spectralJSON.c_str(), false);
    if (!sensorCheckComplete) {
      Serial.printf("Spectral data sent: %s\n", result ? "✅" : "❌");
    }
  }
}

// Simple DHT22 test function
void testDHT22Simple() {
  Serial.println("\n🧪 DHT22 Simple Test (No External Pull-up):");
  Serial.printf("Pin: GPIO%d\n", DHTPIN);

  // Ensure internal pull-up is enabled
  pinMode(DHTPIN, INPUT_PULLUP);
  delay(100);

  // Test pin state
  int pinState = digitalRead(DHTPIN);
  Serial.printf("Pin state: %s (with internal pull-up)\n", pinState ? "HIGH" : "LOW");

  if (!pinState) {
    Serial.println("⚠️ WARNING: Pin is LOW - possible wiring issue");
  }

  // Try basic reading
  Serial.println("Reading sensor...");
  float t = dht.readTemperature();
  float h = dht.readHumidity();

  Serial.printf("Raw values: T=%.2f, H=%.2f\n", t, h);
  Serial.printf("Is NaN? T=%s, H=%s\n", isnan(t) ? "YES" : "NO", isnan(h) ? "YES" : "NO");

  if (!isnan(t) && !isnan(h)) {
    Serial.println("✅ DHT22 is working with internal pull-up!");
  } else {
    Serial.println("❌ DHT22 not responding");
    Serial.println("Solutions:");
    Serial.println("- Add external 4.7kΩ pull-up resistor (DATA to VCC)");
    Serial.println("- Check wiring: VCC→3.3V, GND→GND, DATA→GPIO4");
    Serial.println("- Verify DHT22 sensor (not DHT11)");
    Serial.println("- Try different GPIO pin");
  }
}