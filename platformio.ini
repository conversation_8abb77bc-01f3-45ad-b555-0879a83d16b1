; PlatformIO Project Configuration File
;
;   Build options: build flags, source filter
;   Upload options: custom upload port, speed and extra flags
;   Library options: dependencies, extra library storages
;   Advanced options: extra scripting
;
; Please visit documentation for the other options and examples
; https://docs.platformio.org/page/projectconf.html

[env:esp32dev]
platform = espressif32
board = esp32dev
framework = arduino
monitor_speed = 115200
lib_deps =
    adafruit/DHT sensor library@^1.4.4
    claws/BH1750@^1.3.0
    adafruit/Adafruit INA219@^1.2.1
    sparkfun/SparkFun GridEYE AMG88 Library@^1.0.2
    adafruit/Adafruit AS7341@^1.3.4
    adafruit/Adafruit BusIO@^1.14.1
    knolleary/PubSubClient@^2.8
    bblanchon/Arduino<PERSON>son@^6.21.3
